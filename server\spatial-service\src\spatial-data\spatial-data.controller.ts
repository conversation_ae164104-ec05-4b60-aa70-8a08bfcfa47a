/**
 * 空间数据控制器
 * 提供空间数据的REST API接口
 */
import { 
  Controller, 
  Get, 
  Post, 
  Put, 
  Delete, 
  Body, 
  Param, 
  Query, 
  UseInterceptors,
  UploadedFile,
  HttpStatus,
  HttpException,
  UseGuards
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { 
  ApiTags, 
  ApiOperation, 
  ApiResponse, 
  ApiParam, 
  ApiQuery,
  ApiConsumes,
  ApiBearerAuth
} from '@nestjs/swagger';
import { SpatialDataService } from './spatial-data.service';
import { 
  CreateSpatialFeatureDto, 
  UpdateSpatialFeatureDto,
  SpatialFeatureQueryDto,
  BatchCreateFeaturesDto,
  BatchUpdateFeaturesDto,
  BatchDeleteFeaturesDto,
  FeatureStatisticsDto,
  FeatureResponseDto
} from '../dto/spatial-feature.dto';
import { 
  CreateSpatialLayerDto, 
  UpdateSpatialLayerDto,
  LayerQueryDto,
  LayerExportDto,
  LayerImportDto,
  LayerResponseDto
} from '../dto/spatial-layer.dto';
import { 
  SpatialQueryDto, 
  SpatialAnalysisDto,
  SpatialAnalysisResultDto
} from '../dto/spatial-query.dto';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { CurrentUser } from '../auth/current-user.decorator';

@ApiTags('空间数据')
@Controller('spatial-data')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class SpatialDataController {
  constructor(private readonly spatialDataService: SpatialDataService) {}

  // ==================== 空间要素管理 ====================

  @Post('features')
  @ApiOperation({ summary: '创建空间要素' })
  @ApiResponse({ status: 201, description: '创建成功', type: FeatureResponseDto })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  async createFeature(
    @Body() createFeatureDto: CreateSpatialFeatureDto,
    @CurrentUser() user: any
  ): Promise<FeatureResponseDto> {
    try {
      const feature = await this.spatialDataService.createFeature({
        ...createFeatureDto,
        createdBy: user.id
      });
      return feature;
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  @Get('features/:id')
  @ApiOperation({ summary: '获取空间要素详情' })
  @ApiParam({ name: 'id', description: '要素ID' })
  @ApiResponse({ status: 200, description: '获取成功', type: FeatureResponseDto })
  @ApiResponse({ status: 404, description: '要素不存在' })
  async getFeature(@Param('id') id: string): Promise<FeatureResponseDto> {
    return this.spatialDataService.getFeature(id);
  }

  @Put('features/:id')
  @ApiOperation({ summary: '更新空间要素' })
  @ApiParam({ name: 'id', description: '要素ID' })
  @ApiResponse({ status: 200, description: '更新成功', type: FeatureResponseDto })
  @ApiResponse({ status: 404, description: '要素不存在' })
  async updateFeature(
    @Param('id') id: string,
    @Body() updateFeatureDto: UpdateSpatialFeatureDto,
    @CurrentUser() user: any
  ): Promise<FeatureResponseDto> {
    return this.spatialDataService.updateFeature(id, {
      ...updateFeatureDto,
      updatedBy: user.id
    });
  }

  @Delete('features/:id')
  @ApiOperation({ summary: '删除空间要素' })
  @ApiParam({ name: 'id', description: '要素ID' })
  @ApiResponse({ status: 200, description: '删除成功' })
  @ApiResponse({ status: 404, description: '要素不存在' })
  async deleteFeature(@Param('id') id: string): Promise<{ message: string }> {
    await this.spatialDataService.deleteFeature(id);
    return { message: '删除成功' };
  }

  @Get('features')
  @ApiOperation({ summary: '查询空间要素' })
  @ApiResponse({ status: 200, description: '查询成功' })
  async queryFeatures(@Query() queryDto: SpatialFeatureQueryDto): Promise<{
    features: FeatureResponseDto[];
    total: number;
    page: number;
    pageSize: number;
  }> {
    const { features, total } = await this.spatialDataService.queryFeatures({
      layerId: queryDto.layerId,
      bbox: queryDto.bbox,
      limit: queryDto.limit || 100,
      offset: queryDto.offset || 0
    });

    return {
      features,
      total,
      page: Math.floor((queryDto.offset || 0) / (queryDto.limit || 100)) + 1,
      pageSize: queryDto.limit || 100
    };
  }

  @Post('features/batch')
  @ApiOperation({ summary: '批量创建空间要素' })
  @ApiResponse({ status: 201, description: '批量创建成功' })
  async batchCreateFeatures(
    @Body() batchCreateDto: BatchCreateFeaturesDto,
    @CurrentUser() user: any
  ): Promise<{ 
    success: number; 
    failed: number; 
    errors: string[] 
  }> {
    const results = { success: 0, failed: 0, errors: [] };
    
    for (const featureDto of batchCreateDto.features) {
      try {
        await this.spatialDataService.createFeature({
          ...featureDto,
          createdBy: user.id
        });
        results.success++;
      } catch (error) {
        results.failed++;
        results.errors.push(error.message);
        
        if (!batchCreateDto.ignoreErrors) {
          throw new HttpException(`批量创建失败: ${error.message}`, HttpStatus.BAD_REQUEST);
        }
      }
    }
    
    return results;
  }

  @Put('features/batch')
  @ApiOperation({ summary: '批量更新空间要素' })
  @ApiResponse({ status: 200, description: '批量更新成功' })
  async batchUpdateFeatures(
    @Body() batchUpdateDto: BatchUpdateFeaturesDto,
    @CurrentUser() user: any
  ): Promise<{ 
    success: number; 
    failed: number; 
    errors: string[] 
  }> {
    const results = { success: 0, failed: 0, errors: [] };
    
    for (const featureId of batchUpdateDto.featureIds) {
      try {
        await this.spatialDataService.updateFeature(featureId, {
          ...batchUpdateDto.updateData,
          updatedBy: user.id
        });
        results.success++;
      } catch (error) {
        results.failed++;
        results.errors.push(`${featureId}: ${error.message}`);
        
        if (!batchUpdateDto.ignoreErrors) {
          throw new HttpException(`批量更新失败: ${error.message}`, HttpStatus.BAD_REQUEST);
        }
      }
    }
    
    return results;
  }

  @Delete('features/batch')
  @ApiOperation({ summary: '批量删除空间要素' })
  @ApiResponse({ status: 200, description: '批量删除成功' })
  async batchDeleteFeatures(
    @Body() batchDeleteDto: BatchDeleteFeaturesDto
  ): Promise<{ 
    success: number; 
    failed: number; 
    errors: string[] 
  }> {
    const results = { success: 0, failed: 0, errors: [] };
    
    for (const featureId of batchDeleteDto.featureIds) {
      try {
        await this.spatialDataService.deleteFeature(featureId);
        results.success++;
      } catch (error) {
        results.failed++;
        results.errors.push(`${featureId}: ${error.message}`);
      }
    }
    
    return results;
  }

  // ==================== 空间查询 ====================

  @Post('query/spatial')
  @ApiOperation({ summary: '空间查询' })
  @ApiResponse({ status: 200, description: '查询成功' })
  async spatialQuery(@Body() queryDto: SpatialQueryDto): Promise<FeatureResponseDto[]> {
    return this.spatialDataService.spatialQuery(queryDto);
  }

  @Post('analysis/spatial')
  @ApiOperation({ summary: '空间分析' })
  @ApiResponse({ status: 200, description: '分析成功', type: SpatialAnalysisResultDto })
  async spatialAnalysis(
    @Body() analysisDto: SpatialAnalysisDto,
    @CurrentUser() user: any
  ): Promise<SpatialAnalysisResultDto> {
    try {
      const result = await this.spatialDataService.spatialAnalysis(analysisDto);
      
      return {
        id: `analysis_${Date.now()}`,
        type: analysisDto.type,
        status: 'completed',
        progress: 100,
        result,
        startTime: new Date(),
        endTime: new Date(),
        processingTime: 0,
        parameters: analysisDto.parameters
      };
    } catch (error) {
      return {
        id: `analysis_${Date.now()}`,
        type: analysisDto.type,
        status: 'failed',
        progress: 0,
        error: error.message,
        startTime: new Date(),
        endTime: new Date(),
        processingTime: 0,
        parameters: analysisDto.parameters
      };
    }
  }

  @Get('statistics/features')
  @ApiOperation({ summary: '要素统计' })
  @ApiResponse({ status: 200, description: '统计成功' })
  async getFeatureStatistics(@Query() statisticsDto: FeatureStatisticsDto): Promise<any> {
    // 这里应该实现具体的统计逻辑
    return {
      totalFeatures: 0,
      featuresByType: {},
      totalArea: 0,
      totalLength: 0,
      extent: null
    };
  }

  // ==================== 图层管理 ====================

  @Post('layers')
  @ApiOperation({ summary: '创建图层' })
  @ApiResponse({ status: 201, description: '创建成功', type: LayerResponseDto })
  async createLayer(
    @Body() createLayerDto: CreateSpatialLayerDto,
    @CurrentUser() user: any
  ): Promise<LayerResponseDto> {
    return this.spatialDataService.createLayer({
      ...createLayerDto,
      createdBy: user.id
    });
  }

  @Get('layers')
  @ApiOperation({ summary: '获取图层列表' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getLayers(@Query() queryDto: LayerQueryDto): Promise<{
    layers: LayerResponseDto[];
    total: number;
    page: number;
    pageSize: number;
  }> {
    const layers = await this.spatialDataService.getLayers(queryDto.projectId);
    
    return {
      layers,
      total: layers.length,
      page: Math.floor((queryDto.offset || 0) / (queryDto.limit || 50)) + 1,
      pageSize: queryDto.limit || 50
    };
  }

  // ==================== 数据导入导出 ====================

  @Post('import')
  @ApiOperation({ summary: '导入空间数据' })
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('file'))
  @ApiResponse({ status: 201, description: '导入成功' })
  async importSpatialData(
    @UploadedFile() file: Express.Multer.File,
    @Body() importDto: LayerImportDto,
    @CurrentUser() user: any
  ): Promise<{ layerId: string; featureCount: number; message: string }> {
    if (!file) {
      throw new HttpException('请上传文件', HttpStatus.BAD_REQUEST);
    }

    const result = await this.spatialDataService.importSpatialData(file, {
      ...importDto,
      userId: user.id
    });

    return {
      ...result,
      message: `成功导入 ${result.featureCount} 个要素到图层 ${result.layerId}`
    };
  }

  @Post('export/:layerId')
  @ApiOperation({ summary: '导出空间数据' })
  @ApiParam({ name: 'layerId', description: '图层ID' })
  @ApiResponse({ status: 200, description: '导出成功' })
  async exportSpatialData(
    @Param('layerId') layerId: string,
    @Body() exportDto: LayerExportDto
  ): Promise<any> {
    return this.spatialDataService.exportSpatialData(layerId, exportDto.format);
  }

  // ==================== 健康检查 ====================

  @Get('health')
  @ApiOperation({ summary: '服务健康检查' })
  @ApiResponse({ status: 200, description: '服务正常' })
  async healthCheck(): Promise<{ 
    status: string; 
    timestamp: string; 
    version: string;
    database: string;
  }> {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      database: 'connected'
    };
  }
}
