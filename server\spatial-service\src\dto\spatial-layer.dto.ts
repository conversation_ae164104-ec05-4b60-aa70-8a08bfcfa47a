/**
 * 空间图层DTO
 */
import { IsString, IsOptional, IsObject, IsEnum, IsBoolean, IsNumber, IsArray, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateSpatialLayerDto {
  @ApiProperty({ description: '图层名称' })
  @IsString()
  name: string;

  @ApiPropertyOptional({ description: '图层描述' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ 
    description: '图层类型',
    enum: ['vector', 'raster', 'tile', 'wms', 'wmts']
  })
  @IsEnum(['vector', 'raster', 'tile', 'wms', 'wmts'])
  layerType: 'vector' | 'raster' | 'tile' | 'wms' | 'wmts';

  @ApiProperty({ description: '所属项目ID' })
  @IsString()
  projectId: string;

  @ApiPropertyOptional({ description: '图层元数据' })
  @IsOptional()
  @IsObject()
  metadata?: {
    crs?: string;
    extent?: [number, number, number, number];
    fields?: Array<{
      name: string;
      type: 'string' | 'number' | 'boolean' | 'date' | 'geometry';
      description?: string;
      required?: boolean;
      defaultValue?: any;
    }>;
    source?: {
      type: 'file' | 'database' | 'service' | 'api';
      url?: string;
      connection?: string;
      query?: string;
      format?: string;
    };
    [key: string]: any;
  };

  @ApiPropertyOptional({ description: '图层样式配置' })
  @IsOptional()
  @IsObject()
  style?: {
    default?: {
      color?: string;
      fillColor?: string;
      weight?: number;
      opacity?: number;
      fillOpacity?: number;
      radius?: number;
      strokeWidth?: number;
      strokeColor?: string;
      strokeOpacity?: number;
      iconUrl?: string;
      iconSize?: [number, number];
    };
    rules?: Array<{
      condition: string;
      style: any;
      label?: string;
      description?: string;
    }>;
    legend?: {
      title?: string;
      position?: 'topright' | 'topleft' | 'bottomright' | 'bottomleft';
      items?: Array<{
        label: string;
        color?: string;
        symbol?: string;
      }>;
    };
  };

  @ApiPropertyOptional({ description: '是否可见', default: true })
  @IsOptional()
  @IsBoolean()
  visible?: boolean;

  @ApiPropertyOptional({ description: '是否可编辑', default: true })
  @IsOptional()
  @IsBoolean()
  editable?: boolean;

  @ApiPropertyOptional({ description: '是否可选择', default: true })
  @IsOptional()
  @IsBoolean()
  selectable?: boolean;

  @ApiPropertyOptional({ description: 'Z轴顺序', default: 0 })
  @IsOptional()
  @IsNumber()
  zIndex?: number;

  @ApiPropertyOptional({ description: '透明度', default: 1.0 })
  @IsOptional()
  @IsNumber()
  opacity?: number;

  @ApiPropertyOptional({ description: '最小缩放级别' })
  @IsOptional()
  @IsNumber()
  minZoom?: number;

  @ApiPropertyOptional({ description: '最大缩放级别' })
  @IsOptional()
  @IsNumber()
  maxZoom?: number;

  @ApiPropertyOptional({ description: '权限配置' })
  @IsOptional()
  @IsObject()
  permissions?: {
    read?: string[];
    write?: string[];
    delete?: string[];
    admin?: string[];
  };

  @ApiPropertyOptional({ description: '缓存配置' })
  @IsOptional()
  @IsObject()
  cache?: {
    enabled?: boolean;
    ttl?: number;
    strategy?: 'memory' | 'redis' | 'file';
    maxSize?: number;
  };

  @ApiPropertyOptional({ description: '创建者ID' })
  @IsOptional()
  @IsString()
  createdBy?: string;
}

export class UpdateSpatialLayerDto {
  @ApiPropertyOptional({ description: '图层名称' })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ description: '图层描述' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({ 
    description: '图层类型',
    enum: ['vector', 'raster', 'tile', 'wms', 'wmts']
  })
  @IsOptional()
  @IsEnum(['vector', 'raster', 'tile', 'wms', 'wmts'])
  layerType?: 'vector' | 'raster' | 'tile' | 'wms' | 'wmts';

  @ApiPropertyOptional({ description: '图层元数据' })
  @IsOptional()
  @IsObject()
  metadata?: any;

  @ApiPropertyOptional({ description: '图层样式配置' })
  @IsOptional()
  @IsObject()
  style?: any;

  @ApiPropertyOptional({ description: '是否可见' })
  @IsOptional()
  @IsBoolean()
  visible?: boolean;

  @ApiPropertyOptional({ description: '是否可编辑' })
  @IsOptional()
  @IsBoolean()
  editable?: boolean;

  @ApiPropertyOptional({ description: '是否可选择' })
  @IsOptional()
  @IsBoolean()
  selectable?: boolean;

  @ApiPropertyOptional({ description: 'Z轴顺序' })
  @IsOptional()
  @IsNumber()
  zIndex?: number;

  @ApiPropertyOptional({ description: '透明度' })
  @IsOptional()
  @IsNumber()
  opacity?: number;

  @ApiPropertyOptional({ description: '最小缩放级别' })
  @IsOptional()
  @IsNumber()
  minZoom?: number;

  @ApiPropertyOptional({ description: '最大缩放级别' })
  @IsOptional()
  @IsNumber()
  maxZoom?: number;

  @ApiPropertyOptional({ description: '权限配置' })
  @IsOptional()
  @IsObject()
  permissions?: any;

  @ApiPropertyOptional({ description: '缓存配置' })
  @IsOptional()
  @IsObject()
  cache?: any;
}

export class LayerQueryDto {
  @ApiPropertyOptional({ description: '项目ID' })
  @IsOptional()
  @IsString()
  projectId?: string;

  @ApiPropertyOptional({ description: '图层类型过滤' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  layerTypes?: string[];

  @ApiPropertyOptional({ description: '可见性过滤' })
  @IsOptional()
  @IsBoolean()
  visible?: boolean;

  @ApiPropertyOptional({ description: '搜索关键词' })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({ description: '标签过滤' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiPropertyOptional({ description: '分页大小', default: 50 })
  @IsOptional()
  @IsNumber()
  limit?: number;

  @ApiPropertyOptional({ description: '分页偏移', default: 0 })
  @IsOptional()
  @IsNumber()
  offset?: number;

  @ApiPropertyOptional({ description: '排序字段' })
  @IsOptional()
  @IsString()
  sortBy?: string;

  @ApiPropertyOptional({ description: '排序方向', enum: ['ASC', 'DESC'], default: 'ASC' })
  @IsOptional()
  @IsEnum(['ASC', 'DESC'])
  sortOrder?: 'ASC' | 'DESC';

  @ApiPropertyOptional({ description: '是否包含要素统计', default: false })
  @IsOptional()
  @IsBoolean()
  includeStatistics?: boolean;
}

export class LayerStyleDto {
  @ApiPropertyOptional({ description: '默认样式' })
  @IsOptional()
  @IsObject()
  default?: {
    color?: string;
    fillColor?: string;
    weight?: number;
    opacity?: number;
    fillOpacity?: number;
    radius?: number;
    strokeWidth?: number;
    strokeColor?: string;
    strokeOpacity?: number;
    iconUrl?: string;
    iconSize?: [number, number];
  };

  @ApiPropertyOptional({ description: '样式规则' })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  rules?: Array<{
    condition: string;
    style: any;
    label?: string;
    description?: string;
  }>;

  @ApiPropertyOptional({ description: '图例配置' })
  @IsOptional()
  @IsObject()
  legend?: {
    title?: string;
    position?: 'topright' | 'topleft' | 'bottomright' | 'bottomleft';
    items?: Array<{
      label: string;
      color?: string;
      symbol?: string;
    }>;
  };
}

export class LayerPermissionDto {
  @ApiProperty({ description: '图层ID' })
  @IsString()
  layerId: string;

  @ApiProperty({ description: '用户ID' })
  @IsString()
  userId: string;

  @ApiProperty({ 
    description: '权限类型',
    enum: ['read', 'write', 'delete', 'admin']
  })
  @IsEnum(['read', 'write', 'delete', 'admin'])
  permission: 'read' | 'write' | 'delete' | 'admin';

  @ApiPropertyOptional({ description: '是否授予权限', default: true })
  @IsOptional()
  @IsBoolean()
  grant?: boolean;
}

export class LayerExportDto {
  @ApiProperty({ description: '图层ID' })
  @IsString()
  layerId: string;

  @ApiPropertyOptional({ 
    description: '导出格式',
    enum: ['geojson', 'shapefile', 'kml', 'gpx', 'csv'],
    default: 'geojson'
  })
  @IsOptional()
  @IsEnum(['geojson', 'shapefile', 'kml', 'gpx', 'csv'])
  format?: 'geojson' | 'shapefile' | 'kml' | 'gpx' | 'csv';

  @ApiPropertyOptional({ description: '坐标系统（EPSG代码）' })
  @IsOptional()
  @IsString()
  crs?: string;

  @ApiPropertyOptional({ description: '边界框过滤' })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  bbox?: [number, number, number, number];

  @ApiPropertyOptional({ description: '要素过滤条件' })
  @IsOptional()
  @IsObject()
  filter?: Record<string, any>;

  @ApiPropertyOptional({ description: '导出字段' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  fields?: string[];

  @ApiPropertyOptional({ description: '几何简化容差' })
  @IsOptional()
  @IsNumber()
  simplifyTolerance?: number;

  @ApiPropertyOptional({ description: '最大要素数量' })
  @IsOptional()
  @IsNumber()
  maxFeatures?: number;
}

export class LayerImportDto {
  @ApiProperty({ description: '项目ID' })
  @IsString()
  projectId: string;

  @ApiPropertyOptional({ description: '图层名称' })
  @IsOptional()
  @IsString()
  layerName?: string;

  @ApiPropertyOptional({ description: '图层描述' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({ description: '源坐标系统（EPSG代码）' })
  @IsOptional()
  @IsString()
  sourceCrs?: string;

  @ApiPropertyOptional({ description: '目标坐标系统（EPSG代码）', default: 'EPSG:4326' })
  @IsOptional()
  @IsString()
  targetCrs?: string;

  @ApiPropertyOptional({ description: '编码格式' })
  @IsOptional()
  @IsString()
  encoding?: string;

  @ApiPropertyOptional({ description: '是否覆盖同名图层', default: false })
  @IsOptional()
  @IsBoolean()
  overwrite?: boolean;

  @ApiPropertyOptional({ description: '导入选项' })
  @IsOptional()
  @IsObject()
  options?: {
    validateGeometry?: boolean;
    fixGeometry?: boolean;
    skipInvalidFeatures?: boolean;
    batchSize?: number;
    maxFeatures?: number;
    fieldMapping?: Record<string, string>;
  };
}

export class LayerResponseDto {
  @ApiProperty({ description: '图层ID' })
  id: string;

  @ApiProperty({ description: '图层名称' })
  name: string;

  @ApiPropertyOptional({ description: '图层描述' })
  description?: string;

  @ApiProperty({ description: '图层类型' })
  layerType: string;

  @ApiPropertyOptional({ description: '图层元数据' })
  metadata?: any;

  @ApiPropertyOptional({ description: '图层样式' })
  style?: any;

  @ApiProperty({ description: '所属项目ID' })
  projectId: string;

  @ApiProperty({ description: '是否可见' })
  visible: boolean;

  @ApiProperty({ description: '是否可编辑' })
  editable: boolean;

  @ApiProperty({ description: '是否可选择' })
  selectable: boolean;

  @ApiProperty({ description: 'Z轴顺序' })
  zIndex: number;

  @ApiProperty({ description: '透明度' })
  opacity: number;

  @ApiPropertyOptional({ description: '最小缩放级别' })
  minZoom?: number;

  @ApiPropertyOptional({ description: '最大缩放级别' })
  maxZoom?: number;

  @ApiPropertyOptional({ description: '权限配置' })
  permissions?: any;

  @ApiPropertyOptional({ description: '缓存配置' })
  cache?: any;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;

  @ApiProperty({ description: '创建者' })
  createdBy: string;

  @ApiPropertyOptional({ description: '更新者' })
  updatedBy?: string;

  @ApiPropertyOptional({ description: '要素统计' })
  statistics?: {
    featureCount: number;
    totalArea?: number;
    totalLength?: number;
    extent?: [number, number, number, number];
  };
}
